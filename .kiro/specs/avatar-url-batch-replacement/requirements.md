# Requirements Document

## Introduction

This feature enables batch replacement of avatar URLs in the relationship graph system. Users need to update existing avatar URLs with new ones across multiple categories (中年女, 中年男, 青年女, 青年男, 儿童女, 老年女, 老年男, 儿童男) while maintaining the existing data structure and ensuring all references are properly updated.

## Requirements

### Requirement 1

**User Story:** As a system administrator, I want to batch replace avatar URLs across all relationship categories, so that I can update the avatar resources efficiently without manual editing.

#### Acceptance Criteria

1. WHEN the system processes avatar URL replacement THEN it SHALL replace all occurrences of old URLs with new URLs across all categories
2. WHEN replacing URLs THEN the system SHALL maintain the existing JSON structure and category mappings
3. WHEN replacement is complete THEN the system SHALL preserve all other data integrity

### Requirement 2

**User Story:** As a developer, I want to map specific avatar categories to their new URL sets, so that each relationship type gets the appropriate updated avatars.

#### Acceptance Criteria

1. WHEN processing category mappings THEN the system SHALL map 中年女 to the new midwoman1-4.jpg URLs
2. WHEN processing category mappings THEN the system SHALL map 中年男 to the new midman1-4.jpg URLs  
3. WHEN processing category mappings THEN the system SHALL map 青年女 to the new woman1-4.jpg URLs
4. WHEN processing category mappings THEN the system SHALL map 青年男 to the new man1-4.jpg URLs
5. WHEN processing category mappings THEN the system SHALL map 儿童女 to the new girl1-4.jpg URLs
6. WHEN processing category mappings THEN the system SHALL map 老年女 to the new grandma1-4.jpg URLs
7. WHEN processing category mappings THEN the system SHALL map 老年男 to the new grandpa1-4.jpg URLs
8. WHEN processing category mappings THEN the system SHALL map 儿童男 to the new boy1-4.jpg URLs

### Requirement 3

**User Story:** As a system user, I want the avatar replacement to handle composite categories correctly, so that categories like "妈妈", "爸爸", "朋友", "同事" get updated with the appropriate new URLs.

#### Acceptance Criteria

1. WHEN replacing URLs in composite categories THEN the system SHALL update "妈妈" category with new 中年女 URLs
2. WHEN replacing URLs in composite categories THEN the system SHALL update "爸爸" category with new 中年男 URLs
3. WHEN replacing URLs in composite categories THEN the system SHALL update "儿子" category with appropriate 青年男 and 儿童男 URLs
4. WHEN replacing URLs in composite categories THEN the system SHALL update "女儿" category with appropriate 青年女 and 儿童女 URLs
5. WHEN replacing URLs in composite categories THEN the system SHALL update "爷爷" category with new 老年男 URLs
6. WHEN replacing URLs in composite categories THEN the system SHALL update "奶奶" category with new 老年女 URLs
7. WHEN replacing URLs in composite categories THEN the system SHALL update "朋友", "同事", "默认" categories with mixed appropriate URLs

### Requirement 4

**User Story:** As a developer, I want to ensure data consistency after URL replacement, so that the application continues to function correctly with the new avatar URLs.

#### Acceptance Criteria

1. WHEN URL replacement is complete THEN the system SHALL validate that all URLs follow the new pattern structure
2. WHEN URL replacement is complete THEN the system SHALL ensure no broken or missing URL references exist
3. WHEN URL replacement is complete THEN the system SHALL maintain the same number of avatar options per category
4. IF any URL replacement fails THEN the system SHALL provide clear error messaging and maintain data integrity
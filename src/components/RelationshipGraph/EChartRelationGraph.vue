<template>
  <div class="relationship-graph-container">
    <div ref="chartContainer" class="chart-container"></div>
    <!-- 展示前三个人名的面板 -->
    <div v-if="topThreeNames.length" class="top-names-panel">
      <div class="title">Top 3</div>
      <div class="names">{{ topThreeNames.join('、') }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import userAvatar from '@/assets/icon/img1.png';
import { getAvatarUrl } from '@/utils/avatarUtils';
import { processAvatarImage, shouldProcessImage } from '@/utils/imageProcessor';

// 定义节点和边的数据类型
interface INode {
  id: string;
  label: string;
  type: 'core' | 'other';
  person_id?: string; // 添加person_id字段
  avatar?: string; // 添加avatar字段
  x?: number;
  y?: number;
  fixed?: boolean;
}

interface IEdge {
  source: string;
  target: string;
  label: string;
}

interface IRelationshipData {
  nodes: INode[];
  edges: IEdge[];
}

// Props定义
interface IProps {
  data?: IRelationshipData;
  width?: string;
  height?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  width: '100%',
  height: '400px',
  data: () => ({
    nodes: [
      // 默认核心节点
      { id: 'core', label: '我', type: 'core' },
    ],
    edges: [],
  }),
});

// Emits定义
const emit = defineEmits<{
  'node-click': [nodeData: { id: string; person_id?: string }];
}>();

const chartContainer = ref<HTMLElement>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let chartInstance: any = null;

// 前三位非核心节点的名称
const topThreeNames = ref<string[]>([]);

// 计算节点大小的函数，根据文本长度自适应
const calculateNodeSize = (text: string, nodeType: 'core' | 'other'): number => {
  const baseSize = nodeType === 'core' ? 70 : 30; // 增加基础大小
  const textLength = text.length;

  if (nodeType === 'core') {
    // 核心节点：根据文本长度动态调整大小，确保文本能完整显示
    return Math.max(baseSize, textLength * 3); // 每个字符约8像素宽度，加上边距
  }
  // 其他节点：适度调整
  return Math.max(baseSize, textLength * 2);
};

// 节点类型配置 - 深色主题适配
const nodeTypeConfig = {
  core: {
    color: '#ff6b7a', // 亮红色 - 核心节点，在深色背景下更显眼
    borderColor: '#ff4757',
    borderWidth: 3,
  },
  other: {
    color: '#5c5c5c', // 统一的灰色
    borderColor: '#00ffff', // 浅青色边框
    borderWidth: 3,
  },
};

// 数据转换函数：将原始数据转换为ECharts格式，并处理核心节点定位
const convertToEChartsFormat = async (rawData: IRelationshipData, containerWidth: number, containerHeight: number) => {
  console.log('数据转换 - 容器尺寸:', { containerWidth, containerHeight });

  // 验证输入数据
  if (!rawData || !rawData.nodes || !Array.isArray(rawData.nodes)) {
    console.error('❌ 无效的图表数据结构:', rawData);
    return { nodes: [], centerX: 200, centerY: 200 };
  }

  // 验证容器尺寸
  if (containerWidth <= 0 || containerHeight <= 0) {
    console.warn('容器尺寸无效，使用默认尺寸');
    containerWidth = 400;
    containerHeight = 400;
  }

  const centerX = containerWidth / 2;
  const centerY = containerHeight / 2;
  console.log('计算出的中心点:', { centerX, centerY });

  // 用于处理重复ID的Map，记录每个原始ID出现的次数
  const idCountMap = new Map<string, number>();
  const duplicateIds = new Set<string>();

  // 首先统计每个ID出现的次数
  rawData.nodes.forEach((node) => {
    if (node && typeof node.id === 'string') {
      const count = idCountMap.get(node.id) || 0;
      idCountMap.set(node.id, count + 1);
      if (count > 0) {
        duplicateIds.add(node.id);
      }
    }
  });

  // 如果发现重复ID，记录详细信息
  if (duplicateIds.size > 0) {
    console.warn('⚠️ 检测到重复的节点ID，将为重复节点添加唯一后缀:', Array.from(duplicateIds));
    console.log('📊 重复ID统计:', Object.fromEntries(idCountMap.entries()));
  }

  // 用于跟踪每个原始ID已经使用的次数
  const usedCountMap = new Map<string, number>();
  const nodePromises = rawData.nodes.map(async (node) => {
    // 验证节点数据结构
    if (!node || typeof node.id !== 'string' || typeof node.label !== 'string') {
      console.error('❌ 无效的节点数据:', node);
      return null;
    }

    // 为重复的节点ID生成唯一的ID
    let uniqueId = node.id;
    const originalId = node.id;
    const usedCount = usedCountMap.get(originalId) || 0;

    if (usedCount > 0) {
      // 为重复节点添加后缀，从第二个开始
      uniqueId = `${originalId}_${usedCount}`;
      console.log(`🔄 为重复节点生成唯一ID: ${originalId} -> ${uniqueId}`);
    }

    usedCountMap.set(originalId, usedCount + 1);

    // 验证节点类型
    if (!node.type || (node.type !== 'core' && node.type !== 'other')) {
      console.error('❌ 无效的节点类型:', node.type);
      return null;
    }

    const config = nodeTypeConfig[node.type];
    if (!config) {
      console.error('❌ 找不到节点类型配置:', node.type);
      return null;
    }

    // 根据节点类型计算大小
    const nodeSize = calculateNodeSize(node.label, node.type);

    const baseNode = {
      id: uniqueId, // 使用唯一ID
      name: node.label, // 显示名称保持原样
      originalId, // 保存原始ID用于边的匹配
      category: node.type,
      person_id: node.person_id, // 保留person_id信息
      symbolSize: nodeSize,

      itemStyle: {
        color: config.color,
        borderColor: config.borderColor,
        borderWidth: config.borderWidth,
      },
      label: {
        show: true,
        position: 'bottom', // 将所有节点的文字都放在节点下面
        distance: 10, // 标签与节点的距离
        fontSize: node.type === 'core' ? 14 : 11, // 稍微调小字体以适应更大的节点
        fontWeight: node.type === 'core' ? 'bold' : 'normal',
        color: getComputedStyle(document.documentElement).getPropertyValue('--page-text-primary').trim() || '#ffffff', // 获取CSS变量值
        overflow: 'none', // 确保文本不被截断
        textBorderColor: 'rgba(0,0,0,0.5)', // 增强文本边框提高可读性
        textBorderWidth: 1,
      },
    };

    // 核心节点固定在画布中心
    if (node.type === 'core') {
      // 核心节点优先使用设置的头像，否则使用默认的user_avatar
      const coreAvatarUrl = node.avatar ? getAvatarUrl(node.avatar, node.person_id || node.id) : (userAvatar as string);

      // 对核心节点头像进行预处理
      let processedCoreImage: string;
      if (shouldProcessImage(coreAvatarUrl)) {
        try {
          processedCoreImage = await processAvatarImage(coreAvatarUrl);
        } catch (error) {
          console.warn('核心节点头像预处理失败，使用原始头像:', error);
          processedCoreImage = coreAvatarUrl;
        }
      } else {
        processedCoreImage = coreAvatarUrl;
      }

      const coreNode = {
        ...baseNode,
        x: centerX,
        y: centerY,
        fixed: false, // 允许核心节点被拖拽
        draggable: true, // 允许核心节点拖拽
        repulsion: 300, // 核心节点较小的斥力值
        // 标记为核心节点，用于后续识别
        isCore: true,
        // 使用预处理后的圆形图片作为核心节点的symbol
        symbol: `image://${processedCoreImage}`,
        // 调整核心节点的标签位置到图片下方
        label: {
          ...baseNode.label,
          position: 'bottom',
          distance: 10, // 标签与节点的距离
        },
      };
      console.log('核心节点设置:', coreNode);
      return coreNode;
    }

    // 非核心节点使用头像（优先使用设置的头像，否则使用基于person_id的随机默认头像）
    const avatarUrl = getAvatarUrl(node.avatar, node.person_id || node.id);

    // 对头像进行预处理，添加白色背景并裁剪成圆形
    let processedAvatarUrl: string;
    if (shouldProcessImage(avatarUrl)) {
      try {
        processedAvatarUrl = await processAvatarImage(avatarUrl);
      } catch (error) {
        console.warn('头像预处理失败，使用原始头像:', error);
        processedAvatarUrl = avatarUrl;
      }
    } else {
      processedAvatarUrl = avatarUrl;
    }

    const otherNode = {
      ...baseNode,
      // 使用预处理后的圆形头像作为非核心节点的symbol
      symbol: `image://${processedAvatarUrl}`,
    };

    return otherNode;
  });

  // 等待所有节点处理完成
  const processedNodes = await Promise.all(nodePromises);
  const nodes = processedNodes.filter((node) => node !== null); // 过滤掉无效的节点

  // 在不改变力导向参数与动态效果的前提下，为“非核心节点”设置固定初始角度
  // 规则：角度步长=360/人数；第一个非核心节点位于正上方（-90度）；仅设置初始x/y，不固定半径
  try {
    const others = nodes.filter((n) => n && n.category === 'other');
    const count = others.length;
    if (count > 0) {
      const startAngle = -(7 * Math.PI) / 4; // -135度，左上方
      const step = (2 * Math.PI) / count;
      // 基准半径：基于容器尺寸给一个初始半径，后续由force自行调整
      const baseRadius = Math.min(containerWidth, containerHeight) * 0.35;

      others.forEach((node, i) => {
        const angle = startAngle + i * step;
        const x = centerX + baseRadius * Math.cos(angle);
        const y = centerY + baseRadius * Math.sin(angle);
        // 仅提供初始位置，保持可拖拽与力导向效果
        (node as unknown as { x?: number; y?: number; fixed?: boolean }).x = x;
        (node as unknown as { x?: number; y?: number; fixed?: boolean }).y = y;
        (node as unknown as { fixed?: boolean }).fixed = false;
      });
    }
  } catch (e) {
    console.warn('⚠️ [convertToEChartsFormat] 设置固定初始角度失败，忽略:', e);
  }

  console.log('✅ [convertToEChartsFormat] 数据转换完成:', {
    原始节点数量: rawData.nodes.length,
    有效节点数量: nodes.length,
    重复节点数量: duplicateIds.size,
    核心节点数量: nodes.filter((n) => n && n.category === 'core').length,
    其他节点数量: nodes.filter((n) => n && n.category === 'other').length,
    节点ID列表: nodes.map((n) => n && n.id).filter(Boolean),
  });

  return { nodes, centerX, centerY };
};

// 初始化图表
const initChart = async () => {
  if (!chartContainer.value) {
    console.error('❌ 图表容器未找到');
    return;
  }

  try {
    // 动态导入ECharts
    const echarts = await import('echarts');

    chartInstance = echarts.init(chartContainer.value);

    // 获取画布实际尺寸，添加验证
    let canvasWidth = chartContainer.value.clientWidth;
    let canvasHeight = chartContainer.value.clientHeight;

    console.log('初始画布尺寸:', { canvasWidth, canvasHeight });

    // 验证尺寸，如果为0则等待DOM完全渲染
    if (canvasWidth <= 0 || canvasHeight <= 0) {
      console.warn('容器尺寸为0，等待DOM渲染...');
      await new Promise((resolve) => {
        setTimeout(resolve, 50);
      });
      canvasWidth = chartContainer.value.clientWidth;
      canvasHeight = chartContainer.value.clientHeight;
      console.log('重新获取画布尺寸:', { canvasWidth, canvasHeight });
    }

    // 转换数据格式并处理核心节点定位
    const { nodes } = await convertToEChartsFormat(props.data, canvasWidth, canvasHeight);

    // 更新前三个人名（按当前节点顺序，过滤掉核心节点）
    topThreeNames.value = nodes
      .filter((n: Record<string, unknown>) => n && n.category === 'other')
      .slice(0, 3)
      .map((n: Record<string, unknown>) => (n.name as string) || (n.id as string));

    // 处理边数据 - 深色主题适配，添加数据验证和重复检测
    // 创建原始ID到唯一ID的映射
    const originalIdToUniqueIdMap = new Map<string, string[]>();
    nodes.forEach((node) => {
      const originalId = node.originalId || node.id;
      if (!originalIdToUniqueIdMap.has(originalId)) {
        originalIdToUniqueIdMap.set(originalId, []);
      }
      originalIdToUniqueIdMap.get(originalId)!.push(node.id);
    });

    const seenEdges = new Set<string>();
    const links: Array<{
      source: string;
      target: string;
      label: { show: boolean };
      lineStyle: { color: string; width: number; curveness: number };
    }> = [];

    (props.data.edges || []).forEach((edge) => {
      // 验证边数据结构
      if (!edge || typeof edge.source !== 'string' || typeof edge.target !== 'string') {
        console.error('❌ 无效的边数据:', edge);
        return;
      }

      // 获取源节点和目标节点的所有唯一ID
      const sourceUniqueIds = originalIdToUniqueIdMap.get(edge.source) || [];
      const targetUniqueIds = originalIdToUniqueIdMap.get(edge.target) || [];

      if (sourceUniqueIds.length === 0 || targetUniqueIds.length === 0) {
        console.warn('⚠️ 边引用了不存在的节点:', {
          source: edge.source,
          target: edge.target,
          sourceExists: sourceUniqueIds.length > 0,
          targetExists: targetUniqueIds.length > 0,
        });
        return;
      }

      // 为每个源节点和目标节点的组合创建边
      sourceUniqueIds.forEach((sourceId) => {
        targetUniqueIds.forEach((targetId) => {
          // 创建边的唯一标识符（双向边视为同一条边）
          const edgeKey = [sourceId, targetId].sort().join('-');
          if (seenEdges.has(edgeKey)) {
            return; // 跳过重复的边
          }
          seenEdges.add(edgeKey);

          links.push({
            source: sourceId,
            target: targetId,
            label: {
              show: false, // 隐藏连线上的文字标签
            },
            lineStyle: {
              color: 'transparent', // 完全透明的连线
              width: 2,
              curveness: 0, // 设置为0使连线变为直线
            },
          });
        });
      });
    });

    // 创建分类数据
    const categories = Object.keys(nodeTypeConfig).map((type) => ({
      name: type,
    }));

    const option = {
      backgroundColor: 'transparent', // 透明背景
      tooltip: {
        show: false, // 隐藏tooltip，提供更清洁的UI体验
      },
      legend: {
        show: false,
      },
      // 添加全局动画配置
      animation: true,
      animationDuration: 2000,
      animationEasing: 'cubicOut',
      series: [
        {
          type: 'graph',
          layout: 'force',
          // 使用自定义x/y作为初始布局（在 convertToEChartsFormat 中已按固定角度设置）
          initLayout: 'none',
          data: nodes,
          links,
          categories,
          roam: false, // 禁用画布缩放和平移，但允许节点拖拽
          focusNodeAdjacency: true, // 启用鼠标悬停时高亮相邻节点，但连线保持透明
          draggable: true, // 允许拖拽节点
          left: 'center', // 图表水平居中
          top: 'center', // 图表垂直居中
          force: {
            gravity: 0.08, // 增加向心力，确保节点围绕中心分布并有回弹效果
            repulsion: 250, // 适度的斥力，保持节点间距离
            edgeLength: nodes.length >= 20 ? [70, 110] : [80, 150], // 根据节点数量调整边长度：>=20个节点时使用[70,110]，否则使用[80,150]
            layoutAnimation: true, // 启用布局动画，提供流畅的拖拽回弹效果
            friction: 0.4, // 降低摩擦力，让节点拖拽后有更好的回弹效果
          },
          emphasis: {
            focus: 'adjacency', // 高亮当前节点和相邻节点，其他节点变暗
            itemStyle: {
              color: '#a78bfa', // 悬停时节点变为亮紫色，在深色背景下更显眼
            },
            lineStyle: {
              color: 'transparent', // 悬停时连线仍然保持透明
              width: 2,
            },
          },
          lineStyle: {
            color: 'transparent', // 全局连线样式也设为透明
            curveness: 0, // 设置为0使连线变为直线
          },
          // 添加节点动画效果
          animationDuration: 1000,
          animationEasing: 'elasticOut',
        },
      ],
    };

    chartInstance.setOption(option);

    // 定义ECharts点击事件参数类型
    interface IEchartsClickParams {
      componentType: string;
      seriesType: string;
      data: {
        id: string;
        person_id?: string;
      };
    }

    // 添加节点点击事件监听器
    chartInstance.on('click', (params: IEchartsClickParams) => {
      if (params.componentType === 'series' && params.seriesType === 'graph') {
        const nodeData = params.data;
        console.log('🔄 [EChartRelationGraph] 节点点击事件:', nodeData);

        // 发射节点点击事件，传递节点ID和person_id
        emit('node-click', {
          id: nodeData.id,
          person_id: nodeData.person_id,
        });
      }
    });

    // 添加拖拽结束事件监听器，确保核心节点拖拽后回到中心
    let dragEndTimer: NodeJS.Timeout | null = null;

    // 监听鼠标松开事件，检测拖拽结束
    chartInstance.on('mouseup', () => {
      // 清除之前的定时器
      if (dragEndTimer) {
        clearTimeout(dragEndTimer);
      }

      // 延迟执行，确保拖拽操作完全结束
      dragEndTimer = setTimeout(() => {
        const currentOption = chartInstance.getOption();
        const seriesData = currentOption.series[0].data;

        // 找到核心节点
        const coreNodeIndex = seriesData.findIndex((node: { category: string }) => node.category === 'core');
        if (coreNodeIndex !== -1) {
          const currentCanvasWidth = chartContainer.value?.clientWidth || 400;
          const currentCanvasHeight = chartContainer.value?.clientHeight || 400;
          const centerX = currentCanvasWidth / 2;
          const centerY = currentCanvasHeight / 2;

          const coreNode = seriesData[coreNodeIndex];
          const distanceFromCenter = Math.sqrt((coreNode.x - centerX) ** 2 + (coreNode.y - centerY) ** 2);

          // 如果核心节点距离中心超过5像素，就将其动画回到中心
          if (distanceFromCenter > 5) {
            console.log('🎯 核心节点被拖拽，正在回到中心位置', {
              当前位置: { x: coreNode.x, y: coreNode.y },
              中心位置: { x: centerX, y: centerY },
              距离: distanceFromCenter.toFixed(2),
            });

            // 设置核心节点回到中心，带动画效果
            seriesData[coreNodeIndex].x = centerX;
            seriesData[coreNodeIndex].y = centerY;

            // 更新图表，带动画效果
            chartInstance.setOption({
              series: [
                {
                  data: seriesData,
                  // 添加动画配置
                  animationDuration: 800,
                  animationEasing: 'elasticOut',
                },
              ],
            });
          }
        }
      }, 100); // 100ms延迟，确保拖拽操作完成
    });

    // 优化初始化时序：缩短延迟并添加尺寸验证
    setTimeout(async () => {
      await ensureCoreNodeCentered();
    }, 100); // 缩短延迟到100ms

    // 确保核心节点居中的函数
    const ensureCoreNodeCentered = async () => {
      if (!chartInstance || !chartContainer.value) return;

      // 再次验证容器尺寸
      const currentCanvasWidth = chartContainer.value.clientWidth;
      const currentCanvasHeight = chartContainer.value.clientHeight;

      console.log('确保核心节点居中 - 当前画布尺寸:', {
        currentCanvasWidth,
        currentCanvasHeight,
      });

      // 验证尺寸是否有效
      if (currentCanvasWidth <= 0 || currentCanvasHeight <= 0) {
        console.warn('容器尺寸仍然无效，跳过核心节点定位');
        return;
      }

      const newCenterX = currentCanvasWidth / 2;
      const newCenterY = currentCanvasHeight / 2;
      console.log('重新计算的中心点:', { newCenterX, newCenterY });

      // 重新转换数据并更新核心节点位置
      const { nodes: updatedNodes } = await convertToEChartsFormat(props.data, currentCanvasWidth, currentCanvasHeight);

      // 输出核心节点的实际坐标位置进行验证
      const coreNode = updatedNodes.find((node) => node && node.category === 'core') as
        | {
            x?: number;
            y?: number;
            fixed?: boolean;
          }
        | undefined;
      if (coreNode) {
        console.log('核心节点最终位置:', {
          x: coreNode.x,
          y: coreNode.y,
          fixed: coreNode.fixed,
        });
      }

      chartInstance.setOption({
        series: [
          {
            data: updatedNodes,
          },
        ],
      });
    };

    // 监听窗口大小变化，重新计算核心节点位置
    const handleResize = () => {
      if (!chartInstance || !chartContainer.value) return;

      console.log('窗口大小变化，重新调整核心节点位置');

      // 调整画布大小
      chartInstance.resize();

      // 确保核心节点居中
      setTimeout(async () => {
        await ensureCoreNodeCentered();
      }, 50); // 短暂延迟确保resize完成
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  } catch (error) {
    console.error('❌ 初始化图表失败:', error);
  }
};

// 清除hover状态和高亮状态的方法
const clearHoverState = () => {
  if (chartInstance) {
    console.log('🔄 [EChartRelationGraph] 清除节点hover状态和高亮状态');

    // 清除EChart内置的高亮状态
    chartInstance.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
    });

    // 重新渲染图表以清除自定义高亮样式
    if (props.data) {
      void initChart();
    }
  }
};

// 高亮指定节点的方法
const highlightNodes = (personIds: string[]) => {
  if (!chartInstance) {
    console.warn('⚠️ [EChartRelationGraph] 图表实例不存在，无法高亮节点');
    return;
  }

  console.log('🔄 [EChartRelationGraph] 高亮节点:', personIds);

  // 先清除所有高亮状态
  chartInstance.dispatchAction({
    type: 'downplay',
    seriesIndex: 0,
  });

  // 获取当前图表的数据
  const option = chartInstance.getOption();
  const seriesData = option.series[0].data;

  // 首先重置所有节点的样式到原始状态，移除之前的变暗效果
  const resetSeriesData = seriesData.map((node: Record<string, unknown>) => {
    const resetNode = { ...node };

    // 移除手动设置的itemStyle和label的opacity
    if (resetNode.itemStyle && typeof resetNode.itemStyle === 'object') {
      const itemStyle = { ...(resetNode.itemStyle as Record<string, unknown>) };
      delete itemStyle.opacity;
      resetNode.itemStyle = itemStyle;
    }

    if (resetNode.label && typeof resetNode.label === 'object') {
      const label = { ...(resetNode.label as Record<string, unknown>) };
      delete label.opacity;
      resetNode.label = label;
    }

    return resetNode;
  });

  // 先应用重置后的数据
  chartInstance.setOption({
    series: [
      {
        data: resetSeriesData,
      },
    ],
  });

  // 收集所有匹配的节点索引
  const matchedNodeIndices: number[] = [];

  // 遍历重置后的节点数据，查找匹配的person_id
  resetSeriesData.forEach((node: INode, index: number) => {
    if (node.person_id && personIds.includes(node.person_id)) {
      matchedNodeIndices.push(index);
      console.log(`✨ [EChartRelationGraph] 找到匹配节点，索引: ${index}, person_id: ${node.person_id}`);
    }
  });

  console.log(`🔄 [EChartRelationGraph] 总共找到 ${matchedNodeIndices.length} 个匹配节点，索引:`, matchedNodeIndices);

  // 通过将非搜索结果节点变暗来反向高亮搜索结果
  if (matchedNodeIndices.length > 0) {
    const updatedSeriesData = resetSeriesData.map((node: Record<string, unknown>, index: number) => {
      // 搜索结果节点或核心节点保持原样（正常亮度）
      if (matchedNodeIndices.includes(index) || node.category === 'core') {
        return node;
      }

      // 非搜索结果的非核心节点变暗
      return {
        ...node,
        itemStyle: {
          ...((node.itemStyle as Record<string, unknown>) || {}),
          opacity: 0.3, // 降低透明度使节点变暗
        },
        label: {
          ...((node.label as Record<string, unknown>) || {}),
          opacity: 0.3, // 标签也变暗
        },
      };
    });

    // 更新图表数据以显示变暗效果
    chartInstance.setOption({
      series: [
        {
          data: updatedSeriesData,
        },
      ],
    });

    console.log(`✨ [EChartRelationGraph] 已通过变暗其他节点来高亮 ${matchedNodeIndices.length} 个搜索结果节点`);
  }

  // 如果没有找到任何匹配的节点，输出警告
  if (matchedNodeIndices.length === 0) {
    console.warn('⚠️ [EChartRelationGraph] 未找到任何匹配的节点');
    console.log(
      '🔍 [EChartRelationGraph] 当前节点数据:',
      resetSeriesData.map((node: INode & { name?: string }) => ({
        id: node.id,
        person_id: node.person_id,
        name: node.name || node.label,
      })),
    );
    console.log('🔍 [EChartRelationGraph] 搜索的person_ids:', personIds);
  }

  // 注释掉自动清除高亮的定时器，改为手动清除（当搜索框被清空时）
  // setTimeout(() => {
  //   clearHoverState();
  // }, 10000); // 10秒后清除高亮
};

// 重新调整图表尺寸的方法
const resize = () => {
  if (chartInstance) {
    console.log('🔄 [EChartRelationGraph] 重新调整图表尺寸');
    chartInstance.resize();

    // 延迟确保核心节点居中
    setTimeout(async () => {
      if (chartContainer.value) {
        const currentCanvasWidth = chartContainer.value.clientWidth;
        const currentCanvasHeight = chartContainer.value.clientHeight;

        console.log('🔄 [EChartRelationGraph] resize后的容器尺寸:', {
          currentCanvasWidth,
          currentCanvasHeight,
        });

        // 如果容器尺寸有效，重新转换数据并更新核心节点位置
        if (currentCanvasWidth > 0 && currentCanvasHeight > 0) {
          const { nodes: updatedNodes } = await convertToEChartsFormat(
            props.data,
            currentCanvasWidth,
            currentCanvasHeight,
          );

          chartInstance.setOption({
            series: [
              {
                data: updatedNodes,
              },
            ],
          });
        }
      }
    }, 100);
  }
};

// 暴露方法给父组件
defineExpose({
  clearHoverState,
  highlightNodes,
  resize,
});

// 监听数据变化，重新渲染图表
watch(
  () => props.data,
  async (newData) => {
    if (newData && chartInstance) {
      console.log('🔄 [EChartRelationGraph] 数据变化，重新渲染图表:', newData);

      // 获取画布实际尺寸
      const canvasWidth = chartContainer.value?.clientWidth || 400;
      const canvasHeight = chartContainer.value?.clientHeight || 400;

      // 转换数据格式并处理核心节点定位
      const { nodes } = await convertToEChartsFormat(newData, canvasWidth, canvasHeight);

      // 更新前三个人名
      topThreeNames.value = nodes
        .filter((n: Record<string, unknown>) => n && n.category === 'other')
        .slice(0, 3)
        .map((n: Record<string, unknown>) => (n.name as string) || (n.id as string));

      // 处理边数据，添加数据验证和重复检测
      const seenEdges = new Set<string>();
      const links = (newData.edges || [])
        .map((edge) => {
          // 验证边数据结构
          if (!edge || typeof edge.source !== 'string' || typeof edge.target !== 'string') {
            console.error('❌ 无效的边数据:', edge);
            return null;
          }

          // 检查边的两个节点是否都存在于节点列表中
          const validNodeIds = new Set(nodes.map((node) => node.id));
          if (!validNodeIds.has(edge.source) || !validNodeIds.has(edge.target)) {
            console.warn('⚠️ 边引用了不存在的节点:', {
              source: edge.source,
              target: edge.target,
              sourceExists: validNodeIds.has(edge.source),
              targetExists: validNodeIds.has(edge.target),
            });
            return null;
          }

          // 创建边的唯一标识符（双向边视为同一条边）
          const edgeKey = [edge.source, edge.target].sort().join('-');
          if (seenEdges.has(edgeKey)) {
            console.warn('⚠️ 跳过重复的边:', edgeKey);
            return null;
          }
          seenEdges.add(edgeKey);

          return {
            source: edge.source,
            target: edge.target,
            label: {
              show: false,
            },
            lineStyle: {
              color: 'transparent', // 完全透明的连线
              width: 2,
              curveness: 0,
            },
          };
        })
        .filter((link) => link !== null); // 过滤掉无效的边

      // 更新图表数据
      chartInstance.setOption({
        series: [
          {
            data: nodes,
            links,
          },
        ],
      });

      console.log('✅ [EChartRelationGraph] 图表数据更新完成');
    }
  },
  { deep: true },
);

// ResizeObserver实例
let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  await nextTick();
  await initChart();

  // 添加Intersection Observer来监听容器可见性变化
  if (chartContainer.value) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > 0) {
            console.log('🔄 [EChartRelationGraph] 容器变为可见，重新调整尺寸');
            // 延迟调整尺寸，确保DOM完全渲染
            setTimeout(() => {
              if (chartInstance) {
                chartInstance.resize();
                // 再次确保核心节点居中
                setTimeout(async () => {
                  if (chartContainer.value) {
                    const currentCanvasWidth = chartContainer.value.clientWidth;
                    const currentCanvasHeight = chartContainer.value.clientHeight;

                    if (currentCanvasWidth > 0 && currentCanvasHeight > 0) {
                      const { nodes: updatedNodes } = await convertToEChartsFormat(
                        props.data,
                        currentCanvasWidth,
                        currentCanvasHeight,
                      );

                      chartInstance.setOption({
                        series: [
                          {
                            data: updatedNodes,
                          },
                        ],
                      });
                    }
                  }
                }, 100);
              }
            }, 50);
          }
        });
      },
      {
        threshold: 0.1, // 当10%的容器可见时触发
      },
    );

    observer.observe(chartContainer.value);

    // 在组件卸载时清理observer
    onUnmounted(() => {
      observer.disconnect();
    });
  }
});

onUnmounted(() => {
  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style lang="scss" scoped>
.relationship-graph-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); // 增强阴影效果适配深色主题 - 已移除

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 400px; // 添加最小高度确保容器尺寸正确获取
  }

  // 前三名展示面板样式
  .top-names-panel {
    position: absolute;
    right: 12px;
    top: 12px;
    padding: 8px 10px;
    background: rgba(0, 0, 0, 0.35);
    color: #fff;
    border-radius: 8px;
    backdrop-filter: blur(4px);
    max-width: 40%;
    pointer-events: none; // 不阻挡图表交互

    .title {
      font-size: 12px;
      line-height: 16px;
      opacity: 0.85;
      margin-bottom: 4px;
    }

    .names {
      font-size: 13px;
      line-height: 18px;
      font-weight: 600;
      word-break: break-all;
    }
  }
}
</style>

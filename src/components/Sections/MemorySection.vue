<template>
  <div class="memory-section" :class="{ expanded: isMemoryExpanded }">
    <div class="section-header">
      <span class="section-icon">📝</span>
      <span class="section-title">随手记</span>
      <div class="section-actions">
        <button class="section-tts-btn" title="朗读内容" @click="handleTtsClick">
          <TrumpetIcon class="section-tts-icon" :size="'32px'" />
        </button>
        <button class="section-add-btn" title="添加" @click="handleMemoryAdd">
          <PlusIcon class="add-icon" :size="'32px'" />
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isMemoryExpanded }">
      <div v-if="loadingMemories" class="loading-text">加载中...</div>
      <div v-else-if="memories.length > 0" class="memory-content" :class="{ expanded: isMemoryExpanded }">
        <!-- 记忆项容器 -->
        <div class="memory-items-container">
          <!-- 展开时显示全部，收起时只显示一条 -->
          <div
            v-for="memory in isMemoryExpanded ? memories : memories.slice(0, 1)"
            :key="memory.event_id"
            class="memory-item"
            @click="handleEditMemory(memory)"
          >
            <div class="memory-header">
              <div class="memory-date">
                {{ formatMemoryDate(memory.timestamp) }}
              </div>
              <button class="delete-memory-btn" title="删除" @click.stop="handleDeleteMemory(memory)">
                <DeleteIcon :size="16" color="var(--primary-color)" />
              </button>
            </div>
            <div class="memory-description">{{ memory.description_text }}</div>
            <div v-if="memory.location" class="memory-location">
              <span class="location-icon">📍</span>
              {{ memory.location }}
            </div>
          </div>
        </div>

        <!-- 底部展开/收起按钮 - 当超过一个item时常驻显示 -->
        <div v-if="memories.length > 1" class="content-footer">
          <span class="expand-toggle-text" @click="toggleMemoryExpanded">
            {{ isMemoryExpanded ? '收起' : '展开更多' }}
            <span class="arrow-icon" :class="{ expanded: isMemoryExpanded }">{{ isMemoryExpanded ? '↑' : '↓' }}</span>
          </span>
        </div>
      </div>
      <div v-else class="memory-content">
        <div class="empty-memory">暂无记忆记录，快去和ta聊聊天吧！</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import type { IEvent, IPersonDetail } from '@/apis/memory';
import { getPersonMemories } from '@/apis/memory';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';
import TrumpetIcon from '@/assets/icons/TrumpetIcon.vue';
import PlusIcon from '@/assets/icons/PlusIcon.vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  memoryAdd: [];
  editMemory: [memory: IEvent];
  deleteMemory: [memory: IEvent];
}>();

// TTS相关
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();
const isTtsPlaying = ref(false);
const ttsId = 'memory-section-tts';

// 响应式数据
const isMemoryExpanded = ref(false);
const memories = ref<IEvent[]>([]);
const loadingMemories = ref(false);

// 切换记忆时刻展开/收起状态
const toggleMemoryExpanded = () => {
  isMemoryExpanded.value = !isMemoryExpanded.value;
};

// 格式化记忆日期
const formatMemoryDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// 处理记忆时刻添加按钮点击
const handleMemoryAdd = () => {
  emit('memoryAdd');
};

// 处理编辑记忆
const handleEditMemory = (memory: IEvent) => {
  emit('editMemory', memory);
};

// 处理删除记忆 - 只触发删除事件，不直接修改本地数据
const handleDeleteMemory = (memory: IEvent) => {
  console.log('🗑️ [MemorySection] 触发删除记忆事件:', memory);
  // 只触发父组件的删除事件，让父组件处理确认弹窗和实际删除逻辑
  emit('deleteMemory', memory);
};

// 加载记忆数据
const loadMemories = async () => {
  console.log('🔄 [MemorySection] loadMemories调用，参数:', {
    personId: props.personId,
    userId: props.userId,
  });
  if (!props.personId || !props.userId) {
    memories.value = [];
    return;
  }

  try {
    loadingMemories.value = true;
    console.log('🔄 [MemorySection] 开始获取记忆数据...');

    const response = await getPersonMemories({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [MemorySection] 记忆数据响应:', response);

    if (response && response.result === 'success' && response.events) {
      // 倒序排列，后添加的在上方
      memories.value = response.events;
      console.log('✅ [MemorySection] 记忆数据加载成功，共', memories.value.length, '条记忆');
    } else {
      console.warn('⚠️ [MemorySection] 记忆数据格式异常:', response);
      memories.value = [];
    }
  } catch (error) {
    console.error('❌ [MemorySection] 获取记忆数据失败:', error);
    memories.value = [];
  } finally {
    loadingMemories.value = false;
  }
};

// TTS朗读处理
const handleTtsClick = () => {
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
    isTtsPlaying.value = false;
  } else {
    // 构建朗读内容：读出所有事件的description_text
    const ttsContent = memories.value.map((memory) => memory.description_text).join('。');

    if (ttsContent.trim()) {
      isTtsPlaying.value = true;
      play({
        id: ttsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(ttsId) && audioStatus.value === 'completed') {
          isTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

// 监听props变化，重新加载数据
watch(
  () => [props.personId, props.userId],
  () => {
    void loadMemories();
  },
  { immediate: true },
);

// 监听自定义事件，刷新数据
const handleAddEventSuccess = () => {
  console.log('🔄 [MemorySection] 收到添加事件成功事件，刷新数据');
  void loadMemories();
};

const handleEditEventSuccess = () => {
  console.log('🔄 [MemorySection] 收到编辑事件成功事件，刷新数据');
  void loadMemories();
};

// 组件挂载时加载数据并监听自定义事件
onMounted(() => {
  void loadMemories();

  // 监听自定义事件
  window.addEventListener('addeventsuccess', handleAddEventSuccess);
  window.addEventListener('editeventsuccess', handleEditEventSuccess);
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('addeventsuccess', handleAddEventSuccess);
  window.removeEventListener('editeventsuccess', handleEditEventSuccess);
});

// 暴露刷新方法给父组件
defineExpose({
  loadMemories,
});
</script>

<style lang="scss" scoped>
.memory-section {
  border: none;
  border-radius: 16px;
  padding: 22px 22px 22px 28px; /* 增加左侧padding从22px到28px */
  margin-top: 24px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  display: flex;
  flex-direction: column;
}

// 记忆时刻展开/收起控制 - 修改为固定高度并将展开按钮固定到底部
.memory-section .section-content {
  display: flex;
  flex-direction: column;

  &.expanded {
    min-height: auto; // 展开时取消最小高度限制
  }
}

// 记忆内容区域
.memory-section .memory-content {
  flex: 1;
  overflow: hidden;

  &.expanded {
    overflow: visible;
  }
}

// 底部按钮区域固定到底部
.memory-section .content-footer {
  margin-top: auto; // 自动推到底部
  flex-shrink: 0; // 防止被压缩
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: var(--primary-color);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }
}

.section-add-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  .add-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    transition: all 0.3s ease;
  }
}

.section-tts-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  .section-tts-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }
}

.section-content {
  color: var(--text-primary);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: var(--text-primary);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

// 记忆时刻特有样式
.memory-content {
  // 记忆项容器
  .memory-items-container {
    flex: 1;
    overflow: hidden;

    &.expanded {
      overflow: visible;
    }
  }

  .memory-item {
    background: var(--primary-color-light);
    border: 2px solid var(--border-accent);
    border-radius: 16px;
    padding: 20px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .memory-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-right: 40px;

      .memory-date {
        color: var(--primary-color-timestamp); // 使用主题色时间戳颜色
        font-size: 26px;
        font-weight: 500;
      }

      .delete-memory-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        background: none;
        border: none;
        cursor: pointer;
        color: var(--accent-color);
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        font-size: 18px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .memory-description {
      color: var(--person-detail-context);
      font-size: 32px;
      font-weight: 450;
      line-height: 1.4;
      margin: 8px 0;
    }

    .memory-location {
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 8px;

      .location-icon {
        font-size: 22px;
      }
    }
  }

  .empty-memory {
    color: var(--person-detail-context);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: var(--primary-color-light);
    border: 1px dashed var(--border-accent);
    border-radius: 12px;
    line-height: 1.6;
  }
}

// 底部展开/收起文字样式
.content-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-accent);
}

.expand-toggle-text {
  color: var(--text-primary);
  font-size: 28px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  .arrow-icon {
    font-size: 24px;
    transition: all 0.3s ease;
  }
}
</style>

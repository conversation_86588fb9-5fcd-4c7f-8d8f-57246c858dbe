import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { getColorTheme, saveColorTheme } from '@/apis/user_config';
import type { IThemeConfig } from '@/config/themes';
import { availableThemes, getThemeById, getDefaultTheme } from '@/config/themes';

// 本地存储键名
const THEME_STORAGE_KEY = 'app-theme-id';

export const useThemeStore = defineStore('theme', () => {
  // 当前主题ID
  const currentThemeId = ref<string>('cyberpunk');
  // 可选：当前登录用户ID（由业务层注入），用于与服务器同步主题
  const currentUserId = ref<string | null>(null);

  // 当前主题配置
  const currentTheme = computed<IThemeConfig>(() => {
    return getThemeById(currentThemeId.value) || getDefaultTheme();
  });

  // 所有可用主题
  const themes = computed(() => availableThemes);

  // 初始化主题（可选传入 userId 以从服务器加载）
  const initTheme = async (userId?: string) => {
    if (userId) {
      currentUserId.value = userId;
      try {
        const resp = await getColorTheme(userId);
        const serverId = resp?.config?.color_theme;
        if (serverId && getThemeById(serverId)) {
          currentThemeId.value = serverId;
        }
      } catch (e) {
        console.warn('[ThemeStore] 获取服务器主题失败，回退本地存储', e);
      }
    }
    // 从本地存储读取主题ID
    const savedThemeId = localStorage.getItem(THEME_STORAGE_KEY);
    if (savedThemeId && getThemeById(savedThemeId)) {
      currentThemeId.value = savedThemeId;
    }

    // 应用主题
    applyTheme(currentTheme.value);
  };

  // 切换主题
  const switchTheme = async (themeId: string) => {
    const theme = getThemeById(themeId);
    if (theme) {
      currentThemeId.value = themeId;
      applyTheme(theme);

      // 保存到本地存储
      localStorage.setItem(THEME_STORAGE_KEY, themeId);

      // 若有用户ID，则同步到服务器
      if (currentUserId.value) {
        try {
          await saveColorTheme({ user_id: currentUserId.value, color_theme: themeId });
        } catch (e) {
          console.warn('[ThemeStore] 保存服务器主题失败，已仅写入本地', e);
        }
      }
    }
  };

  // 应用主题到CSS变量
  const applyTheme = (theme: IThemeConfig) => {
    const root = document.documentElement;

    // 更新CSS变量
    root.style.setProperty('--primary-color', theme.colors.primaryColor);
    root.style.setProperty('--primary-color-light', theme.colors.primaryColorLight);
    root.style.setProperty('--primary-color-medium', theme.colors.primaryColorMedium);
    root.style.setProperty('--primary-color-strong', theme.colors.primaryColorStrong);
    root.style.setProperty('--primary-color-timestamp', theme.colors.primaryColorTimeStamp);

    root.style.setProperty('--accent-color', theme.colors.accentColor);
    root.style.setProperty('--accent-color-light', theme.colors.accentColorLight);
    root.style.setProperty('--accent-color-medium', theme.colors.accentColorMedium);
    root.style.setProperty('--accent-color-strong', theme.colors.accentColorStrong);

    root.style.setProperty('--bg-glass', theme.colors.bgGlass);
    root.style.setProperty('--bg-glass-hover', theme.colors.bgGlassHover);
    root.style.setProperty('--bg-glass-popup', theme.colors.bgGlassPopup);
    root.style.setProperty('--border-glass', theme.colors.borderGlass);
    root.style.setProperty('--border-accent', theme.colors.borderAccent);

    root.style.setProperty('--shadow-soft', theme.colors.shadowSoft);
    root.style.setProperty('--shadow-strong', theme.colors.shadowStrong);
    root.style.setProperty('--shadow-accent', theme.colors.shadowAccent);

    root.style.setProperty('--overlay-dark', theme.colors.overlayDark);
    root.style.setProperty('--bg-input', theme.colors.bgInput);
    root.style.setProperty('--bg-input-focus', theme.colors.bgInputFocus);

    root.style.setProperty('--success-color', theme.colors.successColor);
    root.style.setProperty('--success-color-light', theme.colors.successColorLight);
    root.style.setProperty('--success-color-medium', theme.colors.successColorMedium);
    root.style.setProperty('--success-color-strong', theme.colors.successColorStrong);

    root.style.setProperty('--error-color', theme.colors.errorColor);
    root.style.setProperty('--error-color-light', theme.colors.errorColorLight);
    root.style.setProperty('--error-color-medium', theme.colors.errorColorMedium);
    root.style.setProperty('--error-color-strong', theme.colors.errorColorStrong);

    // 更新背景图片变量
    root.style.setProperty('--theme-background-image', `url('${theme.backgroundImage}')`);

    // 更新页面文字颜色变量
    root.style.setProperty('--page-text-primary', theme.colors.pageTextPrimary);
    root.style.setProperty('--page-text-secondary', theme.colors.pageTextSecondary);
    root.style.setProperty('--page-text-tertiary', theme.colors.pageTextTertiary);

    // 同步通用文本变量到当前主题（许多组件使用 --text-*，需要随主题适配）
    root.style.setProperty('--text-primary', theme.colors.pageTextPrimary);
    root.style.setProperty('--text-secondary', theme.colors.pageTextSecondary);
    root.style.setProperty('--text-tertiary', theme.colors.pageTextTertiary);

    // 更新输入框placeholder颜色变量
    root.style.setProperty('--placeholder-color', theme.colors.placeholderColor);

    // 更新PersonDetail组件专用文字颜色变量
    root.style.setProperty('--person-detail-title', theme.colors.personDetailTitle);
    root.style.setProperty('--person-detail-timestamp', theme.colors.personDetailTimestamp);
    root.style.setProperty('--person-detail-context', theme.colors.personDetailContext);

    // 更新ChatItem组件专用文字颜色变量
    root.style.setProperty('--chat-item-user-context', theme.colors.chatItemUserContext);
    root.style.setProperty('--chat-item-assistant-context', theme.colors.chatItemAssistantContext);
    root.style.setProperty('--chat-item-pre-response', theme.colors.chatItemPreResponse);

    console.log(`🎨 [ThemeStore] 已应用主题: ${theme.name}`, theme.backgroundImage);
  };

  // 获取下一个主题（用于循环切换）
  const getNextTheme = () => {
    const currentIndex = themes.value.findIndex((theme) => theme.id === currentThemeId.value);
    const nextIndex = (currentIndex + 1) % themes.value.length;
    return themes.value[nextIndex];
  };

  // 切换到下一个主题
  const switchToNextTheme = () => {
    const nextTheme = getNextTheme();
    switchTheme(nextTheme.id);
  };

  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    console.log(`🎨 [ThemeStore] 主题已切换到: ${newTheme.name}`);
  });

  return {
    // 状态
    currentThemeId,
    currentTheme,
    themes,
    currentUserId,

    // 方法
    initTheme,
    switchTheme,
    switchToNextTheme,
    applyTheme,
    getNextTheme,
    // 业务层可在登录后注入 userId
    setUserId: (userId: string) => {
      currentUserId.value = userId;
    },
  };
});

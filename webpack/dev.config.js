const path = require("path");
const chalk = require("chalk");
const Webpack = require("webpack");
const Merge = require("webpack-merge");
const WebpackDevServer = require("webpack-dev-server");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const BaseConfig = require("./config.js");
const f2eci = require("../f2eci.json");

function resolve(dir) {
  return path.resolve(process.cwd(), dir);
}
/**
 * @type {Webpack.Configuration}
 */
const DevConfig = Merge.merge(BaseConfig, {
  mode: "development",
  entry: {
    index: "./src/main.ts",
  },
  output: {
    publicPath: process.env.PUBLIC_PATH || "/",
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: resolve("public/index.html"),
      filename: "index.html",
      inject: true,
    }),
    new Webpack.DefinePlugin({
      "process.env": {
        VUE_APP_ENV: JSON.stringify(process.env.VUE_APP_ENV),
        VUE_APP_API_HOST: JSON.stringify(process.env.VUE_APP_API_HOST || ''),
        VUE_APP_HTTP_TIMEOUT: JSON.stringify(process.env.VUE_APP_HTTP_TIMEOUT || '30000'),
      },
      __VUE_OPTIONS_API__: true, // 这里必须是布尔值，不能写成字符串
      __VUE_PROD_DEVTOOLS__: false, // 这里必须是布尔值，不能写成字符串
    }),
  ],
  devtool: "source-map",
  devServer: {
    client: {
      progress: true,
      logging: "warn",
      overlay: false,
    },
    host: '0.0.0.0',
    open: true,
    compress: true,
    historyApiFallback: true,
    hot: true,
    port: 8083,
    proxy: {
      "/sso/web/auth": {
        // 不要修改这个target，本地开发测试请使用http://ssodemo.it.test.sankuai.com
        target: "http://ssodemo.it.test.sankuai.com",
        changeOrigin: true,
        xfwd: true,
      },
      "/web/v1/": {
        target: 'https://xiaomeiai.cloud.test.sankuai.com',
        changeOrigin: true,
        secure: false,
      },
      "/weiwei/": {
        target: 'https://xiaomeiai.cloud.test.sankuai.com',
        changeOrigin: true,
        secure: false,
      },
      "/humanrelation/": {
        target: 'https://xiaomeiai.cloud.test.sankuai.com',
        changeOrigin: true,
        secure: false,
      },
    },
  },
});

const compiler = Webpack(DevConfig);
const server = new WebpackDevServer(DevConfig.devServer, compiler);

const runServer = async () => {
  console.log("🚀🚀🚀", chalk.magenta("启动项目"));
  await server.start();
};

runServer();
